<template>
  <div class="login-container">
    <div class="header">
      排队叫号系统
    </div>
    <div class="content-card">
      <div class="welcome-title">欢迎登录</div>
      <div class="welcome-subtitle">请使用分配的账号密码或绑定手机验证码登录系统</div>

      <van-tabs v-model:active="activeTab" line-width="145px" line-height="3px" color="#3B83F7"
        title-active-color="#3B83F7" title-inactive-color="#6C757E">
        <van-tab title="账号登录">
          <div class="form-container">
            <div class="form-item">
              <label class="form-label">账号</label>
              <van-field v-model="username" placeholder="请输入账号" />
            </div>
            <div class="form-item">
              <label class="form-label">密码</label>
              <van-field v-model="password" type="password" placeholder="请输入密码" />
            </div>
            <div class="form-options">
              <van-checkbox v-model="rememberAccount" icon-size="12px">记住账号</van-checkbox>
              <a href="#" class="forgot-password">忘记密码？</a>
            </div>
          </div>
        </van-tab>
        <van-tab title="短验登录">
          <div class="form-container">
            <p style="text-align: center; padding-top: 20px;">暂未开放</p>
          </div>
        </van-tab>
      </van-tabs>

      <van-button type="primary" block class="login-button">登录</van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const activeTab = ref(0);
const username = ref('');
const password = ref('');
const rememberAccount = ref(false);
</script>

<style lang="scss" scoped>
/* Colors */
:root {
  --color-background: #F2F2F2;
  --color-header-bg: #3B83F7;
  --color-header-text: #FFFFFF;
  --color-card-bg: #FFFFFF;
  --color-primary-text: #1978D7;
  --color-secondary-text: #6C757E;
  --color-input-border: #DEE2E6;
  --color-label-text: #495057;
  --color-link: #169BD5;
  --color-checkbox-text: #333333;
}

.login-container {
  background-color: var(--color-background);
  width: 375px;
  height: 797px;
  border-radius: 15px;
  overflow: hidden;
}

.header {
  background-color: var(--color-header-bg);
  color: var(--color-header-text);
  font-size: 16px;
  text-align: center;
  height: 56px;
  line-height: 56px;
}

.content-card {
  background-color: var(--color-card-bg);
  margin: 14px;
  margin-top: -15px;
  /* Overlap with header */
  padding: 0 14px;
  border-radius: 10px;
  position: relative;
  top: 72px;
  height: 710px;
}

.welcome-title {
  padding-top: 42px;
  /* 114 - 72 */
  font-size: 32px;
  color: var(--color-primary-text);
  text-align: center;
  margin-bottom: 18px;
  /* 177 - 114 - 45 */
}

.welcome-subtitle {
  font-family: "PingFangSC-Semibold", sans-serif;
  font-size: 18px;
  color: var(--color-secondary-text);
  text-align: center;
  margin-bottom: 39px;
  /* 293 - 177 - 50 */
}

.form-container {
  padding-top: 34px;
  /* 374 - 293 - 47 */
}

.form-item {
  margin-bottom: 32px;
}

.form-label {
  font-family: "PingFangSC-Semibold", sans-serif;
  font-size: 18px;
  color: var(--color-label-text);
  display: block;
  margin-bottom: 7px;
}

.van-field {
  border: 1px solid var(--color-input-border);
  border-radius: 3px;
  padding: 10px;
  height: 43px;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
  /* 214 - 139 - 43 */
}

.van-checkbox {
  font-size: 13px;
  color: var(--color-checkbox-text);
}

.forgot-password {
  font-size: 13px;
  color: var(--color-link);
  text-decoration: none;
}

.login-button {
  background-color: var(--color-primary-text);
  border-color: var(--color-primary-text);
  border-radius: 5px;
  height: 45px;
  font-size: 16px;
  margin-top: 63px;
  /* 671 - 374 - 234 */
}

:deep(.van-tabs__nav) {
  margin: 0 29px;
  /* (347 - 145*2)/2 */
}

:deep(.van-tab) {
  flex-basis: 50% !important;
}
</style>
