<template>
  <div class="container">
    <van-nav-bar title="排队系统" />
    <div class="main-content">
      <van-card>
        <template #thumb>
          <van-image width="100%" height="170" src="https://img.yzcdn.cn/vant/cat.jpeg" />
        </template>
        <template #title>
          <div class="activity-title">活动标题</div>
        </template>
        <template #desc>
          <div class="activity-desc">我是活动说明我是活动说明我是活动说明我是活动说明我是活动说明我是活动说明我是活动说明</div>
        </template>
        <template #tags>
          <div class="tags-section">
            <div class="tags-label">可预约类型：</div>
            <div class="tags-container">
              <van-tag v-for="tag in appointmentTypes" :key="tag" type="primary" class="appointment-tag">
                {{ tag }}
              </van-tag>
            </div>
          </div>
        </template>
      </van-card>

      <div class="queue-section">
        <van-tabs v-model:active="activeTab" type="card">
          <van-tab v-for="tab in queueTabs" :key="tab" :title="tab"></van-tab>
        </van-tabs>

        <div class="queue-info-card">
          <div class="queue-label">当前排队</div>
          <div class="queue-number">6</div>
          <div class="button-group">
            <van-button type="default" round>立即取号</van-button>
            <van-button type="primary" round>在线预约</van-button>
          </div>
          <a href="#" class="history-link" @click="goToHistory">历史记录 ></a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
const router = useRouter();

const appointmentTypes = ref(['手机清洗', '手机贴膜', '手机回收', '手机维修']);
const queueTabs = ref(['手机清洗', '手机回收', '手机贴膜', '手机维修']);
const activeTab = ref(0);
const goToHistory = () => {
  router.push('/ticketing/history');
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
}

.main-content {
  padding: 16px;
}

.van-card {
  border-radius: 10px;
}

.activity-title {
  font-size: 18px;
  margin-top: 11px;
}

.activity-desc {
  font-size: 16px;
  margin-top: 6px;
  line-height: 1.4;
}

.tags-section {
  margin-top: 14px;
}

.tags-label {
  font-size: 16px;
  margin-bottom: 18px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.appointment-tag {
  font-size: 13px;
  padding: 8px 16px;
  border-radius: 15px;
}

.queue-section {
  margin-top: 16px;
  border-radius: 10px;
  padding: 0 12px 12px;
}

.queue-info-card {
  border: 2px solid #D2E2FF;
  border-radius: 10px;
  padding: 18px;
  text-align: center;
  margin-top: 23px;
}

.queue-label {
  font-size: 18px;
}

.queue-number {
  font-size: 50px;
  margin: 8px 0;
}

.button-group {
  display: flex;
  justify-content: space-around;
  margin-top: 10px;
  padding: 0 20px;
  padding-top: 10px;
}

.history-link {
  display: block;
  margin-top: 21px;
  font-size: 14px;
  text-decoration: none;
}
</style>
