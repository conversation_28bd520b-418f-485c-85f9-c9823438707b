<template>
  <div class="container">
    <van-nav-bar title="排队系统" />
    <div class="page-title">预约记录</div>

    <div class="record-list">
      <van-card v-for="record in records" :key="record.id" class="record-card">
        <template #title>
          <div class="card-header">
            <span class="record-time">预约时间 {{ record.date }} {{ record.time }}</span>
            <van-tag :type="getStatusType(record.status)">{{ record.status }}</van-tag>
          </div>
        </template>
        <template #desc>
          <van-cell-group :border="false">
            <van-cell title="预约服务时间" :value="record.serviceTime" />
            <van-cell title="预约类型" :value="record.type" />
            <van-cell title="预约票号" :value="record.ticketNumber" />
          </van-cell-group>
        </template>
        <template #footer>
          <div v-if="record.status === '预约成功'" class="card-footer">
            <van-button type="primary" round size="small">取消预约</van-button>
          </div>
        </template>
      </van-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const records = ref([
  {
    id: 1,
    date: '2025年6月22日',
    time: '17:35:42',
    status: '已完成',
    serviceTime: '2025年06月25日 10:00~11:00',
    type: '手机贴膜',
    ticketNumber: 'A1002',
  },
  {
    id: 2,
    date: '2025年6月22日',
    time: '17:35:42',
    status: '已过号',
    serviceTime: '2025年06月25日 10:00~11:00',
    type: '手机贴膜',
    ticketNumber: 'A1002',
  },
  {
    id: 3,
    date: '2025年6月22日',
    time: '17:35:42',
    status: '预约成功',
    serviceTime: '2025年06月25日 10:00~11:00',
    type: '手机贴膜',
    ticketNumber: 'A1002',
  },
]);

const getStatusType = (status) => {
  switch (status) {
    case '已完成':
      return 'primary';
    case '预约成功':
      return 'success';
    case '已过号':
      return 'warning';
    default:
      return 'default';
  }
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
}

.page-title {
  font-size: 18px;
  text-align: center;
  padding: 17px 0;
}

.record-list {
  padding: 17px 14px;
  display: flex;
  flex-direction: column;
  gap: 17px;
}

.record-card {
  border-radius: 10px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  font-size: 13px;
}

.van-cell-group {
  padding: 10px 0;
}


.card-footer {
  display: flex;
  justify-content: center;
  padding: 15px 0;
}

.van-button {
  width: 96px;
  height: 40px;
}
</style>
