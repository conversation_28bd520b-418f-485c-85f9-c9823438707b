<template>
  <div class="container">
    <van-nav-bar title="排队叫号系统" />
    <div class="main-content">
      <van-card class="user-info-card">
        <template #title>
          <div class="user-info-header">
            <div class="user-name">李白</div>
            <div class="title">请选择工作窗口</div>
          </div>
        </template>
        <template #footer>
          <van-tabs v-model:active="activeTab">
            <van-tab title="未占用"></van-tab>
            <van-tab title="已占用"></van-tab>
          </van-tabs>
        </template>
      </van-card>

      <div class="window-list">
        <van-card v-for="window in windows" :key="window.id" class="window-card">
          <template #title>
            <div class="window-name">{{ window.name }}</div>
          </template>
          <template #tags>
            <div class="tags">
              <van-tag v-for="tag in window.tags" :key="tag" type="primary">{{ tag }}</van-tag>
            </div>
          </template>
          <template #footer>
            <div class="window-footer">
              <div class="queue-info">
                <div class="queue-count">{{ window.queueCount }}人</div>
                <div class="queue-label">当前排队人数</div>
              </div>
              <van-button size="small" type="primary" @click="goToActivity(window.id)">接入</van-button>
            </div>
          </template>
        </van-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
const $router = useRouter()
const activeTab = ref(0);
const windows = ref([
  {
    id: 1,
    name: '窗口名称一',
    tags: ['手机清洗', '手机回收', '手机贴膜'],
    queueCount: 15,
  },
  {
    id: 2,
    name: '窗口名称二',
    tags: ['手机清洗', '手机回收', '手机贴膜'],
    queueCount: 5,
  },
  {
    id: 3,
    name: '窗口名称三',
    tags: ['手机清洗', '手机回收', '手机贴膜'],
    queueCount: 18,
  },
]);
const goToActivity = (id: string | number) => {
  $router.push(`/calling/activity/${id}`);
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100vh;
}

.main-content {
  padding: 11px 14px;
}

.user-info-card {
  border-radius: 10px;
}

.user-info-header {
  background-color: #F1F8FF;
  padding: 13px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 10px 10px 0 0;
}

.user-name {
  font-size: 24px;
}

.title {
  font-size: 24px;
}

.window-list {
  margin-top: 31px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.window-card {
  border: 1px solid #E9ECEF;
  border-radius: 10px;
}

.window-name {
  font-size: 20px;
  margin-bottom: 14px;
}

.tags {
  display: flex;
  gap: 15px;
}

.window-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 21px;
}

.queue-info {
  display: flex;
  align-items: center;
  gap: 11px;
}

.queue-count {
  font-size: 24px;
}

.queue-label {
  font-size: 14px;
}
</style>
