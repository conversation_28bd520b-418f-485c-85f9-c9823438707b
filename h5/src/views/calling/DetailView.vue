<template>
  <div class="container">
    <van-nav-bar title="排队叫号系统" left-text="返回" left-arrow @click-left="onClickLeft" />
    <div class="main-content">
      <div class="info-header">
        <div class="window-title">窗口：3号服务窗口</div>
      </div>

      <van-cell-group inset class="card-grid">
        <van-cell title-class="card-label" value-class="card-value" title="当前排队人数" value="12" />
        <van-cell title-class="card-label" value-class="card-value" title="上次叫号" value="A012" />
        <van-cell title-class="card-label" value-class="card-value active-value" title="当前票号" value="A016"
          class="active-card" />
      </van-cell-group>

      <div class="button-wrapper">
        <van-button type="primary" block>叫号</van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();
const onClickLeft = () => router.go(-1);
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100vh;
}

.main-content {
  padding: 11px 14px;
}

.info-header {
  border-radius: 10px 10px 0 0;
  padding: 18px;
  text-align: center;
  position: relative;
}

.window-title {
  font-family: var(--font-semibold);
  font-size: 24px;
  color: var(--color-window-title);
  display: inline-block;
}

.card-grid {
  margin-top: 67px;
}

.button-wrapper {
  padding: 0 14px;
  margin-top: 45px;
}
</style>
