import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
    },
    {
      path: '/calling/',
      name: 'calling',
      component: () => import('../views/calling/IndexView.vue'),
    },
    {
      path: '/calling/activity/:id',
      name: 'calling-detail',
      component: () => import('../views/calling/DetailView.vue'),
    },
    {
      path: '/ticketing/activity/:id',
      name: 'ticketing-activity',
      component: () => import('../views/ticketing/ActivityDetail.vue'),
    },
    {
      path: '/ticketing/history',
      name: 'ticketing-history',
      component: () => import('../views/ticketing/BookingHistory.vue'),
    },
  ],
})

export default router
