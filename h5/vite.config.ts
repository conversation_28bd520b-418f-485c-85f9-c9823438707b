import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { VantResolver } from '@vant/auto-import-resolver';
import pxToViewport8 from 'postcss-px-to-viewport-8-plugin';

import collector from './sdk/colllector.js'

const collInstance = await collector.init(
  'yangfan_c', 'gitbj1', 'gittoken', '10.8.8.96:4000'
)
console.log(await collector.getReportByUserTlAndMcp())
// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [VantResolver()],
    }),
    Components({
      resolvers: [VantResolver()],
    }),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    proxy: {
      '/api/': {
        target: 'https://10.8.8.96:4000',
        changeOrigin: true,
        agent: collInstance.agent,
        headers: {
          'Authorization': `Bearer ${collInstance.token}`
        }
      },
    }
  },
  css: {
    postcss: {
      plugins: [
        pxToViewport8({
          unitToConvert: 'px',
          // viewportWidth: file => {
          //   let num = 375;
          //   if (file.indexOf('/node_modules/vant/') !== -1) {
          //     num = 375;
          //   }
          //   return num;
          // },
          viewportWidth: 375,
          unitPrecision: 5,
          propList: ['*'],
          viewportUnit: 'vw',
          fontViewportUnit: 'vw',
          selectorBlackList: [],
          minPixelValue: 2,
          mediaQuery: false,
          replace: true,
          exclude: [],
          landscape: false,
          landscapeUnit: 'vw',
          landscapeWidth: 568
        }),
      ]
    }
  }
})
