import axios from 'axios'
import path from 'path'
import fs from 'fs'
import https from 'https'
import os from 'os'

const globalUser = {
  username: '',
  gitserver: '',
  gittoken: '',
  address: '',
  token: '',
  pfx: null,
  agent: null,
}

const configFath = path.join(os.homedir(), '.smart-agi');
if (!fs.existsSync(configFath)) {
  fs.mkdirSync(configFath, { recursive: true });
}


async function init(username, gitserver, gittoken, address) {
  globalUser.username = username
  globalUser.address = address
  globalUser.token = await getAuthToken(gitserver, gittoken)
  await checkPfx()
  return globalUser
}
async function getAuthToken(gitserver, gittoken) {
  const configPath = path.join(configFath, 'config.json');
  if (fs.existsSync(configPath)) {
    const config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
    return config.token;
  } else {
    const response = await axios.get(`http://${globalUser.address}/api/auth`, {
      headers: {
        'Authorization': `${gitserver} ${gittoken}`
      }
    });
    const configPath = path.join(configFath, 'config.json');
    fs.writeFileSync(configPath, JSON.stringify(response.data));
    return response.data.token;
  }
}

async function checkPfx() {
  if (!fs.existsSync(path.join(configFath, 'client.p12'))) {
    const rep3 = await axios.post(`http://${globalUser.address}/api/certs/issue`, {}, {
      headers: {
        'Authorization': `Bearer ${globalUser.token}`
      }
    });
    fs.writeFileSync(path.join(configFath, 'client.p12'), Buffer.from(rep3.data.content, 'binary'));
  }
  // 读取 P12 证书文件
  globalUser.pfx = fs.readFileSync(path.join(configFath, 'client.p12'));
  const httpsAgent = new https.Agent({
    pfx: globalUser.pfx,
    passphrase: globalUser.username,
    rejectUnauthorized: false // 根据实际情况设置是否验证服务器证书
  });
  globalUser.agent = httpsAgent
}

async function sendActivity(data) {
  try {
    const response = await axios.post(`https://${globalUser.address}/api/user-activity`, data, {
      httpsAgent: globalUser.agent,
      headers: {
        'Authorization': `Bearer ${globalUser.token}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('发送用户活动数据失败:', error);
    throw error;
  }
}

async function getActivity() {
  try {
    const response = await axios.get(`https://${globalUser.address}/api/user-activity`, {
      httpsAgent: globalUser.agent,
      headers: {
        'Authorization': `Bearer ${globalUser.token}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('获取用户活动数据失败:', error);
    throw error;
  }
}
async function getReportByUserTlAndMcp() {
  try {
    const response = await axios.post(`https://${globalUser.address}/api/reports/by-user-tl-mcp`, {
      start_time: '2025-06-01 00:00:00',
      end_time: '2025-06-31 00:00:00'
    }, {
      httpsAgent: globalUser.agent,
      headers: {
        'Authorization': `Bearer ${globalUser.token}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('获取用户活动数据失败:', error);
    throw error;
  }
}

export default { init, sendActivity, getActivity, getReportByUserTlAndMcp }
